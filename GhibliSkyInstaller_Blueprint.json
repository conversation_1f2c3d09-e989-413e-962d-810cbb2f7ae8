{
  "GhibliSkyInstaller": {
    "description": "Single Blueprint that creates the entire Ghibli sky system with one button click",
    "blueprintName": "BP_GhibliSkyInstaller",
    "parentClass": "Actor",
    "category": "Sky Systems",
    "components": [
      {
        "name": "RootComponent",
        "type": "SceneComponent",
        "description": "Root component for the installer"
      },
      {
        "name": "SkySpherePreview",
        "type": "StaticMeshComponent",
        "staticMesh": "/Engine/BasicShapes/Sphere",
        "scale": [50, 50, 50],
        "material": "/Engine/BasicShapes/BasicShapeMaterial",
        "description": "Preview sphere to show installation location"
      },
      {
        "name": "InstallButton",
        "type": "WidgetComponent",
        "widgetClass": "WBP_InstallButton",
        "description": "3D widget button for installation"
      }
    ],
    "variables": [
      {
        "name": "bSystemInstalled",
        "type": "bool",
        "defaultValue": false,
        "description": "Track if system is already installed"
      },
      {
        "name": "InstallationProgress",
        "type": "float",
        "defaultValue": 0.0,
        "description": "Progress of installation (0-1)"
      },
      {
        "name": "CreatedAssets",
        "type": "Array<UObject*>",
        "description": "Track created assets for cleanup"
      },
      {
        "name": "SkySphereActor",
        "type": "AActor*",
        "description": "Reference to created sky sphere"
      },
      {
        "name": "InstallationSettings",
        "type": "struct",
        "struct": "FGhibliSkySettings",
        "description": "Configuration for the sky system"
      }
    ],
    "structs": [
      {
        "name": "FGhibliSkySettings",
        "description": "Settings for Ghibli sky installation",
        "properties": [
          {
            "name": "SkyScale",
            "type": "float",
            "defaultValue": 500.0,
            "description": "Scale of the sky sphere"
          },
          {
            "name": "InitialTimeOfDay",
            "type": "float",
            "defaultValue": 0.5,
            "description": "Starting time of day"
          },
          {
            "name": "InitialCloudDensity",
            "type": "float",
            "defaultValue": 0.6,
            "description": "Starting cloud density"
          },
          {
            "name": "AutoAnimateTime",
            "type": "bool",
            "defaultValue": true,
            "description": "Auto-animate time of day"
          },
          {
            "name": "PresetStyle",
            "type": "enum",
            "enumName": "EGhibliPreset",
            "defaultValue": "TotoroSummer",
            "description": "Initial style preset"
          }
        ]
      }
    ],
    "enums": [
      {
        "name": "EGhibliPreset",
        "values": [
          "TotoroSummer",
          "SpiritedAwayOpening",
          "CastleInTheSky",
          "HowlsMovingCastle",
          "Custom"
        ]
      }
    ],
    "events": [
      {
        "name": "BeginPlay",
        "description": "Initialize the installer",
        "nodes": [
          {
            "type": "Branch",
            "condition": "bSystemInstalled == false",
            "true": [
              {
                "type": "SetActorHiddenInGame",
                "target": "SkySpherePreview",
                "hidden": false
              },
              {
                "type": "SetVisibility",
                "target": "InstallButton",
                "visible": true
              }
            ],
            "false": [
              {
                "type": "SetActorHiddenInGame",
                "target": "SkySpherePreview",
                "hidden": true
              },
              {
                "type": "SetVisibility",
                "target": "InstallButton",
                "visible": false
              }
            ]
          }
        ]
      }
    ],
    "functions": [
      {
        "name": "InstallGhibliSkySystem",
        "description": "Main installation function - creates entire sky system",
        "accessModifier": "Public",
        "returnType": "bool",
        "nodes": [
          {
            "type": "Branch",
            "condition": "bSystemInstalled == true",
            "true": [
              {
                "type": "PrintString",
                "string": "Ghibli Sky System already installed!",
                "color": "Yellow"
              },
              {
                "type": "ReturnNode",
                "value": false
              }
            ]
          },
          {
            "type": "PrintString",
            "string": "Installing Ghibli Sky System...",
            "color": "Green"
          },
          {
            "type": "CallFunction",
            "function": "CreateMaterialParameterCollection",
            "output": "MPCSuccess"
          },
          {
            "type": "CallFunction",
            "function": "CreateCloudNoiseMaterialFunction",
            "output": "MF1Success"
          },
          {
            "type": "CallFunction",
            "function": "CreateSoftEdgeMaterialFunction",
            "output": "MF2Success"
          },
          {
            "type": "CallFunction",
            "function": "CreateGhibliSkyMaterial",
            "output": "MaterialSuccess"
          },
          {
            "type": "CallFunction",
            "function": "CreateSkySphereActor",
            "output": "ActorSuccess"
          },
          {
            "type": "CallFunction",
            "function": "ApplyInitialSettings"
          },
          {
            "type": "AND",
            "inputs": ["MPCSuccess", "MF1Success", "MF2Success", "MaterialSuccess", "ActorSuccess"],
            "output": "AllSuccess"
          },
          {
            "type": "Branch",
            "condition": "AllSuccess",
            "true": [
              {
                "type": "Set",
                "variable": "bSystemInstalled",
                "value": true
              },
              {
                "type": "SetActorHiddenInGame",
                "target": "SkySpherePreview",
                "hidden": true
              },
              {
                "type": "SetVisibility",
                "target": "InstallButton",
                "visible": false
              },
              {
                "type": "PrintString",
                "string": "Ghibli Sky System installed successfully!",
                "color": "Green"
              },
              {
                "type": "ReturnNode",
                "value": true
              }
            ],
            "false": [
              {
                "type": "PrintString",
                "string": "Installation failed! Check output log for details.",
                "color": "Red"
              },
              {
                "type": "CallFunction",
                "function": "CleanupFailedInstallation"
              },
              {
                "type": "ReturnNode",
                "value": false
              }
            ]
          }
        ]
      },
      {
        "name": "CreateMaterialParameterCollection",
        "description": "Creates the MPC_GhibliSky parameter collection",
        "returnType": "bool",
        "implementation": {
          "description": "Uses UKismetSystemLibrary::CreateAsset to generate MPC",
          "steps": [
            "Create new MaterialParameterCollection asset",
            "Add scalar parameters: TimeOfDay, CloudDensity, CloudSpeed, AtmosphereIntensity",
            "Add vector parameter: WindDirection",
            "Set default values",
            "Save asset to /Game/GhibliSky/MPC_GhibliSky"
          ]
        }
      },
      {
        "name": "CreateCloudNoiseMaterialFunction",
        "description": "Creates the cloud noise material function",
        "returnType": "bool",
        "implementation": {
          "description": "Programmatically builds material function node graph",
          "nodeGraph": [
            "FunctionInput: UV (Vector2)",
            "FunctionInput: Scale (Scalar, default 1.0)",
            "FunctionInput: Octaves (Scalar, default 4.0)",
            "Noise node: SimplexNoise with Scale * 0.5",
            "Noise node: SimplexNoise with Scale * 2.0 for detail",
            "Noise node: VoronoiNoise with Scale * 0.3",
            "Multiply: BaseNoise * (DetailNoise * 0.3)",
            "Add: Combined + (VoronoiNoise * 0.4)",
            "Power: Result ^ 1.5",
            "FunctionOutput: Final noise value"
          ]
        }
      },
      {
        "name": "CreateSoftEdgeMaterialFunction",
        "description": "Creates the soft edge material function",
        "returnType": "bool",
        "implementation": {
          "description": "Creates function for painterly cloud edges",
          "nodeGraph": [
            "FunctionInput: CloudMask (Scalar)",
            "FunctionInput: Softness (Scalar, default 0.3)",
            "Smoothstep: min=(0.3-Softness), max=(0.7+Softness), value=CloudMask",
            "Power: Result ^ 0.8",
            "FunctionOutput: Soft edge result"
          ]
        },
        {
          "name": "CreateGhibliSkyMaterial",
          "description": "Creates the main sky material with complete node graph",
          "returnType": "bool",
          "implementation": {
            "description": "Builds the complete Ghibli sky material programmatically",
            "materialSettings": {
              "materialDomain": "Surface",
              "blendMode": "Opaque",
              "shadingModel": "Unlit",
              "twoSided": false
            },
            "parameters": [
              "CloudScale1 (Scalar, 0.8)",
              "CloudScale2 (Scalar, 1.5)",
              "CloudOpacity1 (Scalar, 0.9)",
              "CloudOpacity2 (Scalar, 0.6)",
              "SkyColorTop (Vector, 0.4,0.7,1.0)",
              "SkyColorHorizon (Vector, 0.8,0.9,1.0)",
              "CloudColorBright (Vector, 1.0,0.95,0.9)",
              "CloudColorShadow (Vector, 0.6,0.7,0.8)"
            ],
            "nodeGraph": "Complete material graph as defined in main system"
          }
        },
        {
          "name": "CreateSkySphereActor",
          "description": "Creates and configures the sky sphere actor",
          "returnType": "bool",
          "nodes": [
            {
              "type": "SpawnActor",
              "class": "StaticMeshActor",
              "transform": "GetActorTransform()",
              "output": "NewSkySphere"
            },
            {
              "type": "SetStaticMesh",
              "target": "NewSkySphere.StaticMeshComponent",
              "mesh": "/Engine/BasicShapes/Sphere"
            },
            {
              "type": "SetWorldScale3D",
              "target": "NewSkySphere",
              "scale": "InstallationSettings.SkyScale, InstallationSettings.SkyScale, InstallationSettings.SkyScale"
            },
            {
              "type": "SetMaterial",
              "target": "NewSkySphere.StaticMeshComponent",
              "materialIndex": 0,
              "material": "CreatedGhibliSkyMaterial"
            },
            {
              "type": "SetCastShadow",
              "target": "NewSkySphere.StaticMeshComponent",
              "castShadow": false
            },
            {
              "type": "SetReceivesDecals",
              "target": "NewSkySphere.StaticMeshComponent",
              "receivesDecals": false
            },
            {
              "type": "SetActorLabel",
              "target": "NewSkySphere",
              "label": "GhibliSkySphere"
            },
            {
              "type": "Set",
              "variable": "SkySphereActor",
              "value": "NewSkySphere"
            }
          ]
        },
        {
          "name": "ApplyInitialSettings",
          "description": "Apply initial settings based on chosen preset",
          "nodes": [
            {
              "type": "Switch",
              "input": "InstallationSettings.PresetStyle",
              "cases": {
                "TotoroSummer": {
                  "TimeOfDay": 0.5,
                  "CloudDensity": 0.3,
                  "SkyColorTop": [0.4, 0.7, 1.0],
                  "SkyColorHorizon": [0.9, 0.9, 0.8],
                  "CloudColorBright": [1.0, 1.0, 0.9],
                  "CloudColorShadow": [0.6, 0.7, 0.8]
                },
                "SpiritedAwayOpening": {
                  "TimeOfDay": 0.3,
                  "CloudDensity": 0.4,
                  "SkyColorTop": [0.3, 0.5, 0.9],
                  "SkyColorHorizon": [0.9, 0.7, 0.5],
                  "CloudColorBright": [1.0, 0.9, 0.7],
                  "CloudColorShadow": [0.4, 0.5, 0.7]
                },
                "CastleInTheSky": {
                  "TimeOfDay": 0.6,
                  "CloudDensity": 0.8,
                  "SkyColorTop": [0.2, 0.4, 0.8],
                  "SkyColorHorizon": [0.8, 0.6, 0.4],
                  "CloudColorBright": [0.9, 0.8, 0.6],
                  "CloudColorShadow": [0.3, 0.4, 0.6]
                },
                "HowlsMovingCastle": {
                  "TimeOfDay": 0.8,
                  "CloudDensity": 0.7,
                  "SkyColorTop": [0.2, 0.3, 0.6],
                  "SkyColorHorizon": [0.9, 0.5, 0.3],
                  "CloudColorBright": [0.9, 0.6, 0.4],
                  "CloudColorShadow": [0.3, 0.2, 0.4]
                }
              }
            },
            {
              "type": "SetMaterialParameterCollectionScalarParameter",
              "collection": "MPC_GhibliSky",
              "parameter": "TimeOfDay",
              "value": "SelectedPreset.TimeOfDay"
            },
            {
              "type": "SetMaterialParameterCollectionScalarParameter",
              "collection": "MPC_GhibliSky",
              "parameter": "CloudDensity",
              "value": "SelectedPreset.CloudDensity"
            }
          ]
        },
        {
          "name": "CleanupFailedInstallation",
          "description": "Clean up any partially created assets",
          "nodes": [
            {
              "type": "ForEachLoop",
              "array": "CreatedAssets",
              "loopBody": [
                {
                  "type": "IsValid",
                  "object": "ArrayElement",
                  "validExecution": [
                    {
                      "type": "DestroyActor",
                      "target": "ArrayElement"
                    }
                  ]
                }
              ]
            },
            {
              "type": "ClearArray",
              "array": "CreatedAssets"
            }
          ]
        },
        {
          "name": "UninstallSystem",
          "description": "Remove the installed sky system",
          "accessModifier": "Public",
          "nodes": [
            {
              "type": "Branch",
              "condition": "bSystemInstalled == true",
              "true": [
                {
                  "type": "IsValid",
                  "object": "SkySphereActor",
                  "validExecution": [
                    {
                      "type": "DestroyActor",
                      "target": "SkySphereActor"
                    }
                  ]
                },
                {
                  "type": "Set",
                  "variable": "bSystemInstalled",
                  "value": false
                },
                {
                  "type": "SetActorHiddenInGame",
                  "target": "SkySpherePreview",
                  "hidden": false
                },
                {
                  "type": "SetVisibility",
                  "target": "InstallButton",
                  "visible": true
                },
                {
                  "type": "PrintString",
                  "string": "Ghibli Sky System uninstalled",
                  "color": "Yellow"
                }
              ]
            }
          ]
        }
      ]
    },
    "widgetBlueprint": {
      "name": "WBP_InstallButton",
      "description": "UI widget for the installation button",
      "widgets": [
        {
          "type": "Button",
          "name": "InstallButton",
          "text": "Install Ghibli Sky",
          "style": {
            "normalColor": [0.2, 0.6, 1.0, 0.8],
            "hoveredColor": [0.3, 0.7, 1.0, 1.0],
            "pressedColor": [0.1, 0.5, 0.9, 1.0],
            "fontSize": 24,
            "fontColor": [1.0, 1.0, 1.0, 1.0]
          }
        },
        {
          "type": "ProgressBar",
          "name": "InstallProgress",
          "visible": false,
          "fillColor": [0.2, 0.8, 0.3, 1.0]
        },
        {
          "type": "TextBlock",
          "name": "StatusText",
          "text": "Ready to install",
          "fontSize": 16
        }
      ],
      "events": [
        {
          "name": "OnInstallButtonClicked",
          "description": "Handle install button click",
          "nodes": [
            {
              "type": "GetOwningPlayerPawn",
              "output": "PlayerPawn"
            },
            {
              "type": "GetActorOfClass",
              "class": "BP_GhibliSkyInstaller",
              "output": "InstallerActor"
            },
            {
              "type": "CallFunction",
              "target": "InstallerActor",
              "function": "InstallGhibliSkySystem"
            }
          ]
        }
      ]
    },
    "installationInstructions": {
      "title": "Drag & Drop Ghibli Sky Installation",
      "steps": [
        {
          "step": 1,
          "description": "Drag BP_GhibliSkyInstaller from Content Browser into your level"
        },
        {
          "step": 2,
          "description": "Position the installer where you want the sky center (usually at world origin)"
        },
        {
          "step": 3,
          "description": "Click the 'Install Ghibli Sky' button that appears above the installer"
        },
        {
          "step": 4,
          "description": "Wait for installation to complete (progress shown on button)"
        },
        {
          "step": 5,
          "description": "Enjoy your Studio Ghibli-style sky! The installer will hide itself after completion"
        }
      ],
      "notes": [
        "The installer creates all materials, functions, and parameter collections automatically",
        "You can delete the installer actor after installation if desired",
        "Multiple presets are available in the installer's details panel",
        "The sky sphere will be created at the installer's location with proper scale"
      ]
    }
  }
}
