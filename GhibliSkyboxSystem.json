{"UnrealEngineGhibliSkyboxSystem": {"description": "Complete Studio Ghibli-style dynamic skybox system for Unreal Engine", "version": "1.0", "components": {"MaterialParameterCollection": {"name": "MPC_Ghi<PERSON>liSky", "description": "Global parameters for the sky system", "parameters": [{"name": "TimeOfDay", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.5, "description": "0=Dawn, 0.25=Morning, 0.5=<PERSON><PERSON>, 0.75=Evening, 1=Night"}, {"name": "CloudDensity", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.6, "description": "Overall cloud coverage (0-1)"}, {"name": "CloudSpeed", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.1, "description": "Speed of cloud movement"}, {"name": "WindDirection", "type": "Vector", "defaultValue": [1, 0.3, 0], "description": "Wind direction for cloud movement"}, {"name": "AtmosphereIntensity", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 1.0, "description": "Intensity of atmospheric scattering"}]}, "MaterialFunctions": {"CloudNoiseFunction": {"name": "MF_G<PERSON>liCloudNoise", "description": "Generates organic, puffy cloud shapes", "inputs": [{"name": "UV", "type": "Vector2", "description": "UV coordinates"}, {"name": "Scale", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 1.0}, {"name": "Octaves", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 4.0}, {"name": "Persistence", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.5}], "materialGraph": {"nodes": [{"type": "Noise", "function": "SimplexNoise", "scale": "Scale * 0.5", "octaves": "Octaves", "description": "Base cloud shape"}, {"type": "Noise", "function": "SimplexNoise", "scale": "Scale * 2.0", "octaves": "Octaves - 1", "description": "Detail noise for cloud edges"}, {"type": "Noise", "function": "VoronoiNoise", "scale": "Scale * 0.3", "description": "Large cloud formations"}, {"type": "Math", "operation": "Multiply", "inputs": ["BaseNoise", "DetailNoise * 0.3"], "description": "Combine base and detail"}, {"type": "Math", "operation": "Add", "inputs": ["CombinedNoise", "VoronoiNoise * 0.4"], "description": "Add large formations"}, {"type": "Power", "base": "FinalNoise", "exponent": 1.5, "description": "Enhance cloud definition"}]}}, "SoftEdgeFunction": {"name": "MF_GhibliSoftEdge", "description": "Creates soft, painterly cloud edges", "inputs": [{"name": "CloudMask", "type": "<PERSON><PERSON><PERSON>"}, {"name": "Softness", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.3}], "materialGraph": {"nodes": [{"type": "Smoothstep", "min": "0.3 - Softness", "max": "0.7 + Softness", "input": "CloudMask", "description": "Create soft transitions"}, {"type": "Power", "base": "SmoothstepResult", "exponent": 0.8, "description": "Soften further for painterly look"}]}}}, "MainSkyMaterial": {"name": "M_G<PERSON>bliSky", "description": "Master material for Studio Ghibli-style sky", "materialDomain": "Surface", "blendMode": "Opaque", "shadingModel": "Unlit", "parameters": [{"name": "CloudScale1", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.8, "description": "Scale for primary cloud layer"}, {"name": "CloudScale2", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 1.5, "description": "Scale for secondary cloud layer"}, {"name": "CloudOpacity1", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.9, "description": "Opacity of primary clouds"}, {"name": "CloudOpacity2", "type": "<PERSON><PERSON><PERSON>", "defaultValue": 0.6, "description": "Opacity of secondary clouds"}, {"name": "SkyColorTop", "type": "Vector", "defaultValue": [0.4, 0.7, 1.0], "description": "Sky color at zenith"}, {"name": "SkyColorHorizon", "type": "Vector", "defaultValue": [0.8, 0.9, 1.0], "description": "Sky color at horizon"}, {"name": "CloudColorBright", "type": "Vector", "defaultValue": [1.0, 0.95, 0.9], "description": "Bright cloud color"}, {"name": "CloudColorShadow", "type": "Vector", "defaultValue": [0.6, 0.7, 0.8], "description": "Shadow cloud color"}], "materialGraph": {"description": "Complete material node graph for Ghibli-style sky", "nodes": [{"type": "WorldPosition", "output": "WorldPosition", "description": "Get world position for UV mapping"}, {"type": "ComponentMask", "input": "WorldPosition", "mask": "RG", "output": "WorldUV", "description": "Extract XY for UV coordinates"}, {"type": "Divide", "input": "WorldUV", "value": 10000, "output": "NormalizedUV", "description": "Normalize world coordinates"}, {"type": "Time", "output": "GameTime", "description": "Get current game time"}, {"type": "CollectionParameter", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "CloudSpeed", "output": "CloudSpeed"}, {"type": "CollectionParameter", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "WindDirection", "output": "WindDirection"}, {"type": "Multiply", "inputs": ["GameTime", "CloudSpeed"], "output": "AnimationTime"}, {"type": "Multiply", "inputs": ["WindDirection.RG", "AnimationTime"], "output": "CloudOffset"}, {"type": "Add", "inputs": ["NormalizedUV", "CloudOffset"], "output": "AnimatedUV"}, {"type": "MaterialFunction", "function": "MF_G<PERSON>liCloudNoise", "inputs": {"UV": "AnimatedUV", "Scale": "CloudScale1", "Octaves": 4, "Persistence": 0.5}, "output": "CloudLayer1"}, {"type": "MaterialFunction", "function": "MF_G<PERSON>liCloudNoise", "inputs": {"UV": "AnimatedUV * 0.7", "Scale": "CloudScale2", "Octaves": 3, "Persistence": 0.6}, "output": "CloudLayer2"}, {"type": "CollectionParameter", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "CloudDensity", "output": "CloudDensity"}, {"type": "Multiply", "inputs": ["CloudLayer1", "CloudDensity"], "output": "DensityAdjustedClouds1"}, {"type": "Multiply", "inputs": ["CloudLayer2", "CloudDensity * 0.8"], "output": "DensityAdjustedClouds2"}, {"type": "MaterialFunction", "function": "MF_GhibliSoftEdge", "inputs": {"CloudMask": "DensityAdjustedClouds1", "Softness": 0.4}, "output": "SoftClouds1"}, {"type": "MaterialFunction", "function": "MF_GhibliSoftEdge", "inputs": {"CloudMask": "DensityAdjustedClouds2", "Softness": 0.3}, "output": "SoftClouds2"}, {"type": "CameraVector", "output": "CameraVector", "description": "Get camera direction for sky gradient"}, {"type": "ComponentMask", "input": "CameraVector", "mask": "B", "output": "VerticalGradient", "description": "Extract vertical component"}, {"type": "Saturate", "input": "VerticalGradient", "output": "SaturatedGradient"}, {"type": "LinearInterpolate", "A": "SkyColorHorizon", "B": "SkyColorTop", "Alpha": "SaturatedGradient", "output": "SkyGradient", "description": "Create sky color gradient"}, {"type": "CollectionParameter", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "TimeOfDay", "output": "TimeOfDay"}, {"type": "<PERSON><PERSON>", "input": "TimeOfDay * 6.28318", "output": "SunAngle", "description": "Calculate sun position"}, {"type": "Add", "inputs": ["SunAngle", 1], "output": "SunIntensity"}, {"type": "Divide", "input": "SunIntensity", "value": 2, "output": "NormalizedSunIntensity"}, {"type": "Power", "base": "SoftClouds1", "exponent": 0.7, "output": "CloudLighting1", "description": "Enhance cloud lighting"}, {"type": "LinearInterpolate", "A": "CloudColorShadow", "B": "CloudColorBright", "Alpha": "CloudLighting1 * NormalizedSunIntensity", "output": "LitClouds1"}, {"type": "LinearInterpolate", "A": "CloudColorShadow * 0.8", "B": "CloudColorBright * 0.9", "Alpha": "SoftClouds2 * NormalizedSunIntensity", "output": "LitClouds2"}, {"type": "LinearInterpolate", "A": "SkyGradient", "B": "LitClouds1", "Alpha": "SoftClouds1 * CloudOpacity1", "output": "SkyWithClouds1"}, {"type": "LinearInterpolate", "A": "SkyWithClouds1", "B": "LitClouds2", "Alpha": "SoftClouds2 * CloudOpacity2", "output": "FinalSkyColor"}, {"type": "CollectionParameter", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "AtmosphereIntensity", "output": "AtmosphereIntensity"}, {"type": "Multiply", "inputs": ["FinalSkyColor", "AtmosphereIntensity"], "output": "AtmosphericSky", "description": "Apply atmospheric intensity"}], "outputs": {"BaseColor": "AtmosphericSky", "Emissive": "AtmosphericSky * 0.1", "Roughness": 1.0, "Metallic": 0.0}}}, "SkySphereBlueprint": {"name": "BP_GhibliSkySphere", "description": "Complete sky sphere setup with <PERSON><PERSON><PERSON><PERSON> material", "parentClass": "Actor", "components": [{"name": "SkySphereComponent", "type": "StaticMeshComponent", "staticMesh": "/Engine/BasicShapes/Sphere", "material": "M_G<PERSON>bliSky", "scale": [500, 500, 500], "castShadow": false, "receiveDecals": false}], "variables": [{"name": "TimeOfDaySpeed", "type": "float", "defaultValue": 0.01, "description": "Speed of time progression"}, {"name": "AutoAnimateTime", "type": "bool", "defaultValue": true, "description": "Automatically animate time of day"}, {"name": "CloudAnimationSpeed", "type": "float", "defaultValue": 1.0, "description": "Multiplier for cloud movement speed"}], "blueprintGraph": {"events": [{"name": "BeginPlay", "description": "Initialize sky system", "nodes": [{"type": "SetMaterialParameterCollection", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "TimeOfDay", "value": 0.5}, {"type": "SetMaterialParameterCollection", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "CloudSpeed", "value": "CloudAnimationSpeed * 0.1"}]}, {"name": "Tick", "description": "Update sky parameters each frame", "condition": "AutoAnimateTime == true", "nodes": [{"type": "GetMaterialParameterCollection", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "TimeOfDay", "output": "CurrentTime"}, {"type": "Add", "inputs": ["CurrentTime", "TimeOfDaySpeed * DeltaTime"], "output": "NewTime"}, {"type": "Fmod", "input": "NewTime", "divisor": 1.0, "output": "WrappedTime", "description": "Keep time between 0-1"}, {"type": "SetMaterialParameterCollection", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "TimeOfDay", "value": "WrappedTime"}]}], "functions": [{"name": "SetTimeOfDay", "description": "Manually set time of day", "inputs": [{"name": "Time", "type": "float", "description": "Time value 0-1"}], "nodes": [{"type": "C<PERSON>", "input": "Time", "min": 0.0, "max": 1.0, "output": "ClampedTime"}, {"type": "SetMaterialParameterCollection", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "TimeOfDay", "value": "ClampedTime"}]}, {"name": "SetCloudDensity", "description": "Adjust cloud coverage", "inputs": [{"name": "Density", "type": "float", "description": "Cloud density 0-1"}], "nodes": [{"type": "C<PERSON>", "input": "Density", "min": 0.0, "max": 1.0, "output": "ClampedDensity"}, {"type": "SetMaterialParameterCollection", "collection": "MPC_Ghi<PERSON>liSky", "parameter": "CloudDensity", "value": "ClampedDensity"}]}, {"name": "SetWeatherPreset", "description": "Apply predefined weather settings", "inputs": [{"name": "WeatherType", "type": "enum", "values": ["Clear", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Overcast", "Stormy"]}], "nodes": [{"type": "Switch", "input": "WeatherType", "cases": {"Clear": {"CloudDensity": 0.2, "CloudSpeed": 0.05, "AtmosphereIntensity": 1.2}, "PartlyCloudy": {"CloudDensity": 0.6, "CloudSpeed": 0.1, "AtmosphereIntensity": 1.0}, "Overcast": {"CloudDensity": 0.9, "CloudSpeed": 0.15, "AtmosphereIntensity": 0.8}, "Stormy": {"CloudDensity": 1.0, "CloudSpeed": 0.3, "AtmosphereIntensity": 0.6}}}]}]}}, "AnimationController": {"name": "BP_GhibliSkyController", "description": "Advanced controller for sky animations and transitions", "parentClass": "Actor", "variables": [{"name": "TimelineComponent", "type": "TimelineComponent", "description": "For smooth transitions"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "enum", "values": ["Clear", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Overcast", "Stormy"], "defaultValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "TransitionDuration", "type": "float", "defaultValue": 5.0, "description": "Duration for weather transitions"}], "functions": [{"name": "TransitionToWeather", "description": "Smoothly transition between weather states", "inputs": [{"name": "TargetWeather", "type": "enum"}, {"name": "Duration", "type": "float", "defaultValue": 5.0}], "implementation": "Uses timeline component to interpolate between current and target weather parameters"}, {"name": "CreateGhibliMoment", "description": "Create specific cinematic sky moments", "inputs": [{"name": "MomentType", "type": "enum", "values": ["SpiritedAwayOpening", "CastleInTheSky", "TotoroSummer", "HowlsMovingCastle"]}], "presets": {"SpiritedAwayOpening": {"TimeOfDay": 0.3, "CloudDensity": 0.4, "SkyColorTop": [0.3, 0.5, 0.9], "SkyColorHorizon": [0.9, 0.7, 0.5], "CloudColorBright": [1.0, 0.9, 0.7], "CloudColorShadow": [0.4, 0.5, 0.7]}, "CastleInTheSky": {"TimeOfDay": 0.6, "CloudDensity": 0.8, "SkyColorTop": [0.2, 0.4, 0.8], "SkyColorHorizon": [0.8, 0.6, 0.4], "CloudColorBright": [0.9, 0.8, 0.6], "CloudColorShadow": [0.3, 0.4, 0.6]}, "TotoroSummer": {"TimeOfDay": 0.5, "CloudDensity": 0.3, "SkyColorTop": [0.4, 0.7, 1.0], "SkyColorHorizon": [0.9, 0.9, 0.8], "CloudColorBright": [1.0, 1.0, 0.9], "CloudColorShadow": [0.6, 0.7, 0.8]}, "HowlsMovingCastle": {"TimeOfDay": 0.8, "CloudDensity": 0.7, "SkyColorTop": [0.2, 0.3, 0.6], "SkyColorHorizon": [0.9, 0.5, 0.3], "CloudColorBright": [0.9, 0.6, 0.4], "CloudColorShadow": [0.3, 0.2, 0.4]}}}]}}, "setupInstructions": {"description": "Step-by-step setup guide for the Ghibli skybox system", "steps": [{"step": 1, "title": "Create Material Parameter Collection", "description": "Create a new Material Parameter Collection asset named 'MPC_GhibliSky' and add all the scalar and vector parameters listed in the MaterialParameterCollection section"}, {"step": 2, "title": "Create Material Functions", "description": "Create the material functions MF_GhibliCloudNoise and MF_GhibliSoftEdge using the node graphs provided"}, {"step": 3, "title": "Create Main Sky Material", "description": "Create a new Material named 'M_G<PERSON><PERSON>liSky' with the complete material graph. Set Material Domain to Surface, Blend Mode to Opaque, and Shading Model to Unlit"}, {"step": 4, "title": "Create Sky Sphere Blueprint", "description": "Create a new Blueprint Actor named 'BP_GhibliSkySphere' with a Static Mesh Component using the Engine sphere mesh scaled to 500x500x500, and apply the M_GhibliSky material"}, {"step": 5, "title": "Create Animation Controller", "description": "Create the BP_GhibliSkyController blueprint for advanced sky control and cinematic presets"}, {"step": 6, "title": "Place in Level", "description": "Add BP_GhibliSkySphere to your level and optionally add BP_GhibliSkyController for dynamic control"}], "tips": ["Adjust CloudScale1 and CloudScale2 parameters to change cloud size and detail", "Use the weather presets for quick atmospheric changes", "The cinematic presets recreate specific moments from <PERSON><PERSON><PERSON><PERSON> films", "For best results, disable the default sky sphere in your level", "Consider adding post-processing effects like bloom for enhanced atmosphere"]}, "technicalNotes": {"performance": "The system uses efficient noise functions and parameter collections for optimal performance", "compatibility": "Compatible with Unreal Engine 4.26+ and Unreal Engine 5.x", "customization": "All parameters are exposed for easy customization and can be animated via blueprints or sequencer", "lighting": "Works best with directional lighting that matches the TimeOfDay parameter"}}}