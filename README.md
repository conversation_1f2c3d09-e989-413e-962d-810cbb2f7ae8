# Studio Ghibli Dynamic Skybox System for Unreal Engine

A complete material system that recreates the characteristic puffy, painterly cloud aesthetic seen in Studio Ghibli films like "Spirited Away," with soft edges and dreamy atmospheric qualities.

## 🎨 Features

- **Painterly Cloud Aesthetics** - Soft, organic cloud shapes with characteristic Ghibli-style edges
- **Dynamic Time-of-Day System** - Automatic or manual control of lighting and atmosphere
- **Multi-Layer Cloud Generation** - Complex, realistic cloud formations using advanced noise functions
- **Cinematic Presets** - Recreate specific atmospheric moments from Ghibli films
- **Weather System** - Quick transitions between Clear, Partly Cloudy, Overcast, and Stormy conditions
- **Performance Optimized** - Efficient material system suitable for real-time rendering

## 📋 Requirements

- Unreal Engine 4.26+ or Unreal Engine 5.x
- Basic knowledge of Unreal Engine materials and blueprints

## 🚀 Quick Setup Guide

### Step 1: Create Material Parameter Collection
1. In Content Browser, right-click → **Materials & Textures** → **Material Parameter Collection**
2. Name it `MPC_GhibliSky`
3. Add the following parameters:
   - **Scalar Parameters:**
     - `TimeOfDay` (Default: 0.5)
     - `CloudDensity` (Default: 0.6)
     - `CloudSpeed` (Default: 0.1)
     - `AtmosphereIntensity` (Default: 1.0)
   - **Vector Parameters:**
     - `WindDirection` (Default: 1, 0.3, 0)

### Step 2: Create Material Functions
Create two Material Functions in your Content Browser:

#### MF_GhibliCloudNoise
- **Purpose:** Generates organic, puffy cloud shapes
- **Inputs:** UV (Vector2), Scale (Scalar), Octaves (Scalar), Persistence (Scalar)
- **Implementation:** Combine Simplex Noise + Voronoi Noise with multiple octaves

#### MF_GhibliSoftEdge
- **Purpose:** Creates soft, painterly cloud edges
- **Inputs:** CloudMask (Scalar), Softness (Scalar)
- **Implementation:** Use Smoothstep + Power nodes for soft transitions

### Step 3: Create Main Sky Material
1. Create new Material named `M_GhibliSky`
2. Set **Material Domain** to `Surface`
3. Set **Blend Mode** to `Opaque`
4. Set **Shading Model** to `Unlit`
5. Add material parameters:
   - `CloudScale1` (Scalar, Default: 0.8)
   - `CloudScale2` (Scalar, Default: 1.5)
   - `CloudOpacity1` (Scalar, Default: 0.9)
   - `CloudOpacity2` (Scalar, Default: 0.6)
   - `SkyColorTop` (Vector, Default: 0.4, 0.7, 1.0)
   - `SkyColorHorizon` (Vector, Default: 0.8, 0.9, 1.0)
   - `CloudColorBright` (Vector, Default: 1.0, 0.95, 0.9)
   - `CloudColorShadow` (Vector, Default: 0.6, 0.7, 0.8)

### Step 4: Build Material Graph
Connect the following node chain:
1. **World Position** → Normalize to UV coordinates
2. **Time + Wind Direction** → Animate cloud movement
3. **Material Functions** → Generate two cloud layers
4. **Collection Parameters** → Control density and animation
5. **Sky Gradient** → Blend horizon to zenith colors
6. **Cloud Lighting** → Apply time-of-day lighting
7. **Final Blend** → Combine sky and clouds

### Step 5: Create Sky Sphere Blueprint
1. Create new Blueprint Actor named `BP_GhibliSkySphere`
2. Add **Static Mesh Component**
3. Set Static Mesh to `/Engine/BasicShapes/Sphere`
4. Set Scale to `500, 500, 500`
5. Apply `M_GhibliSky` material
6. Disable **Cast Shadow** and **Receive Decals**

### Step 6: Add Blueprint Logic
In `BP_GhibliSkySphere`, add:
- **Variables:**
  - `TimeOfDaySpeed` (Float, Default: 0.01)
  - `AutoAnimateTime` (Bool, Default: true)
  - `CloudAnimationSpeed` (Float, Default: 1.0)

- **Event BeginPlay:** Initialize parameter collection values
- **Event Tick:** Update TimeOfDay parameter when AutoAnimateTime is true

### Step 7: Create Animation Controller (Optional)
Create `BP_GhibliSkyController` for advanced features:
- Weather transition system
- Cinematic presets
- Timeline-based animations

## 🎬 Cinematic Presets

The system includes presets inspired by specific Ghibli films:

- **Spirited Away Opening** - Warm morning atmosphere
- **Castle in the Sky** - Dramatic cloudy skies  
- **Totoro Summer** - Bright, cheerful day
- **Howl's Moving Castle** - Moody evening atmosphere

## ⚙️ Usage

### Basic Usage
1. Place `BP_GhibliSkySphere` in your level
2. Disable any existing sky sphere
3. The sky will automatically animate

### Manual Control
```cpp
// Set time of day (0=Dawn, 0.25=Morning, 0.5=Noon, 0.75=Evening, 1=Night)
SetTimeOfDay(0.3f);

// Adjust cloud density (0-1)
SetCloudDensity(0.8f);

// Apply weather preset
SetWeatherPreset(WeatherType::PartlyCloudy);
```

### Parameter Tweaking
- **CloudScale1/2** - Adjust cloud size and detail
- **CloudOpacity1/2** - Control cloud visibility
- **Sky Colors** - Customize atmosphere colors
- **Cloud Colors** - Adjust cloud lighting

## 🔧 Performance Tips

- The system is optimized for real-time use
- Uses efficient noise functions and parameter collections
- Consider LOD systems for distant views
- Adjust cloud octaves based on performance needs

## 🎨 Customization Tips

- Modify noise scales for different cloud types
- Adjust color palettes for various moods
- Use Sequencer to animate parameters for cutscenes
- Combine with post-processing for enhanced atmosphere

## 📝 Technical Notes

- Compatible with Lumen and traditional lighting
- Works with both forward and deferred rendering
- Supports VR and high-resolution displays
- Material instances can be created for level-specific variations

## 🐛 Troubleshooting

**Clouds not appearing:**
- Check Material Parameter Collection is properly referenced
- Verify cloud density > 0
- Ensure material is applied to sky sphere

**Performance issues:**
- Reduce noise octaves in material functions
- Lower cloud layer complexity
- Use material instances instead of modifying base material

**Colors look wrong:**
- Verify sRGB settings on color parameters
- Check post-processing tone mapping
- Ensure proper lighting setup

## 📄 License

This system is provided as-is for educational and commercial use in Unreal Engine projects.

---

*Inspired by the beautiful atmospheric work in Studio Ghibli films. Create your own magical skies!*
